# Application Settings
ENV=
APP_NAME=api-gateway
DEBUG=true
API_V1_STR=/api/v1

# Service endpoints
USER_SERVICE_HOST=localhost
USER_SERVICE_PORT=50052
ADMIN_SERVICE_HOST=localhost
ADMIN_SERVICE_PORT=50053
COMMUNICATION_SERVICE_HOST=localhost
COMMUNICATION_SERVICE_PORT=50055
WORKFLOW_SERVICE_HOST=localhost
WORKFLOW_SERVICE_PORT=50056
AGENT_SERVICE_HOST=localhost
AGENT_SERVICE_PORT=50057
MCP_SERVICE_HOST=localhost
MCP_SERVICE_PORT=50058
NOTIFICATION_SERVICE_HOST=localhost
NOTIFICATION_SERVICE_PORT=50060
ORGANISATION_SERVICE_HOST=localhost
ORGANISATION_SERVICE_PORT=50070

# Redis settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# JWT settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS settings
CORS_ORIGINS=["http://localhost", "http://localhost:3000", "http://localhost:8000"]
CORS_CREDENTIALS=true
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# Service discovery settings
SERVICE_DISCOVERY_ENABLED=false
SERVICE_DISCOVERY_HOST=consul
SERVICE_DISCOVERY_PORT=8500

# Proto
REPO_URL=
GIT_TOKEN=

# Google OAuth
GOOGLE_CLIENT_ID="google-client-id"
GOOGLE_CLIENT_SECRET="google-client-secret"
GOOGLE_REDIRECT_URI=

# Kafka Settings
KAFKA_BROKER_PORT=9092
KAFKA_BROKER_HOST=localhost

# API Key For get_workflow_orchestration route
ORCHESTRATION_SERVER_AUTH_KEY=

# LiveKit Configuration
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=
LIVEKIT_URL=

AGENT_PLATFORM_AUTH_KEY=
WORKFLOW_SERVICE_AUTH_KEY=

GCS_CRED=
BUCKET_NAME=



OPENAI_API_KEY=
OPENAI_MODEL=gpt-4o