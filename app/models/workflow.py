from datetime import datetime
import uuid
from sqlalchemy import (
    Column,
    String,
    DateTime,
    Integer,
    Float,
    ForeignKey,
    Enum,
    JSON,
    Boolean,
    ARRAY,
    Text,  # For potentially longer descriptions/changelogs
)
from sqlalchemy.orm import declarative_base, relationship

# Assuming your enums are defined in this path
from app.utils.constants.constants import (
    WorkflowCategoryEnum,
    WorkflowOwnerTypeEnum,
    WorkflowVisibilityEnum,
    WorkflowStatusEnum,
)

Base = declarative_base()

# --- Core Workflow Tables ---


class Workflow(Base):
    __tablename__ = "workflows"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    image_url = Column(String, nullable=True)

    workflow_url = Column(String, nullable=False)
    builder_url = Column(String, nullable=False)
    start_nodes = Column(JSON, nullable=False, default=list)

    owner_id = Column(String, nullable=False)
    owner_type = Column(Enum(WorkflowOwnerTypeEnum), nullable=False)

    # Link to the current active version
    current_version_id = Column(String, ForeignKey("workflow_versions.id"), nullable=True)

    # If this workflow was created by cloning from another workflow
    is_imported = Column(Boolean, default=False)
    workflow_template_id = Column(
        String, ForeignKey("workflows.id"), nullable=True
    )
    template_owner_id = Column(String, nullable=True)
    is_changes_marketplace = Column(Boolean, default=False)
    is_customizable = Column(Boolean, default=True)

    # Version control flag - when True, creates new version on every update
    auto_version_on_update = Column(Boolean, default=False)

    user_ids = Column(ARRAY(String), nullable=True, default=list)

    visibility = Column(
        Enum(WorkflowVisibilityEnum), nullable=False, default=WorkflowVisibilityEnum.PRIVATE
    )
    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    category = Column(Enum(WorkflowCategoryEnum), nullable=True)
    tags = Column(JSON, nullable=True, default=list)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to all its versions
    versions = relationship(
        "WorkflowVersion",
        back_populates="workflow",
        cascade="all, delete-orphan",
        foreign_keys="WorkflowVersion.workflow_id"
    )
    # Relationship to the current active version
    current_version = relationship(
        "WorkflowVersion",
        foreign_keys=[current_version_id],
        post_update=True
    )
    # Relationship to its listings on the marketplace
    marketplace_listings = relationship(
        "WorkflowMarketplaceListing",
        back_populates="workflow",
        foreign_keys="WorkflowMarketplaceListing.workflow_id"
    )

    # Self-referencing relationship for workflow templates
    # Workflows created from this workflow (children)
    derived_workflows = relationship(
        "Workflow",
        foreign_keys="Workflow.workflow_template_id",
        remote_side="Workflow.id",
        backref="source_workflow"
    )

    def __repr__(self):
        return f"<Workflow id={self.id} name='{self.name}'>"


class WorkflowVersion(Base):
    __tablename__ = "workflow_versions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    workflow_id = Column(String, ForeignKey("workflows.id"), nullable=False)

    version_number = Column(String(50), nullable=False, default="1.0.0")
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String, nullable=True)

    workflow_url = Column(String, nullable=False)
    builder_url = Column(String, nullable=False)
    start_nodes = Column(JSON, nullable=False, default=list)

    category = Column(Enum(WorkflowCategoryEnum), nullable=True)
    tags = Column(JSON, nullable=True, default=list)
    changelog = Column(Text, nullable=True)

    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    is_customizable = Column(Boolean, default=True)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    workflow = relationship("Workflow", back_populates="versions", foreign_keys=[workflow_id])

    marketplace_listings = relationship(
        "WorkflowMarketplaceListing", back_populates="workflow_version"
    )

    def __repr__(self):
        return f"<WorkflowVersion id={self.id} workflow_id='{self.workflow_id}' version='{self.version_number}'>"


class WorkflowMarketplaceListing(Base):
    __tablename__ = "workflow_marketplace_listings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Link to the specific version being listed
    workflow_version_id = Column(String, ForeignKey("workflow_versions.id"), nullable=False)
    # Link back to the parent workflow for easier querying if needed
    workflow_id = Column(String, ForeignKey("workflows.id"), nullable=False)

    listed_by_user_id = Column(String, nullable=False)  # User who created this listing

    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String, nullable=True)

    category = Column(Enum(WorkflowCategoryEnum), nullable=True)
    tags = Column(ARRAY(String), nullable=True, default=list)

    use_count = Column(Integer, default=0)
    execution_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)

    visibility = Column(
        Enum(WorkflowVisibilityEnum), nullable=False, default=WorkflowVisibilityEnum.PUBLIC
    )

    status = Column(Enum(WorkflowStatusEnum), nullable=False, default=WorkflowStatusEnum.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    workflow_version = relationship("WorkflowVersion", back_populates="marketplace_listings")
    workflow = relationship("Workflow", back_populates="marketplace_listings", foreign_keys=[workflow_id])

    def __repr__(self):
        return f"<WorkflowMarketplaceListing id={self.id} title='{self.title}' version_id='{self.workflow_version_id}'>"
