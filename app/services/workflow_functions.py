# app/services/workflow_service.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.models.workflow_rating import WorkflowRating
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
)
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.json_validator import validate_transition_schema
from app.utils.file_upload import GCSUploadService
from app.utils.kafka.kafka_service import KafkaProducer


# Initialize structured logger
logger = structlog.get_logger()


class WorkflowFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createWorkflow(
        self, request: workflow_pb2.CreateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateWorkflowResponse:
        db = SessionLocal()
        logger.info("create_workflow_request", name=request.name)
        try:
            # Parse workflow_data from string to JSON
            print("[DEBUG] Attempting to parse workflow_data JSON")
            try:
                workflow_data = json.loads(request.workflow_data)
                print("[DEBUG] Successfully parsed workflow_data JSON")
            except json.JSONDecodeError as e:
                print(f"[DEBUG] JSON parsing failed: {str(e)}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in workflow_data")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message="Invalid JSON format in workflow_data"
                )

            # Upload original workflow to GCS
            print("[DEBUG] Uploading workflow to GCS")
            try:
                file_upload = GCSUploadService()
                gcs_response = file_upload.upload_json_as_file(workflow_data, "workflow_builders")
                builder_url = gcs_response.get("publicUrl")
                print("[DEBUG] GCS upload successful, got builder_url")
                if not builder_url:
                    print("[DEBUG] Failed to get public URL from GCS response")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to obtain public URL from GCS for builder workflow")
                    return workflow_pb2.CreateWorkflowResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for builder workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for builder workflow: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"GCS upload failed for builder workflow: {str(e)}"
                )

            # Convert workflow to transition schema
            print("[DEBUG] Converting workflow to transition schema")
            try:
                converted_workflow = convert_workflow_to_transition_schema(workflow_data)
                print("[DEBUG] Workflow conversion successful")
            except Exception as e:
                print(f"[DEBUG] Workflow conversion failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Workflow schema conversion failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Workflow schema conversion failed: {str(e)}"
                )

            # Validate converted workflow against transition_schema.json
            print("[DEBUG] Validating against transition_schema.json")
            try:
                validate_transition_schema(
                    data_input=converted_workflow,
                    schema_path="app/utils/shared/json_schemas/transition_schema.json",
                )
                print("[DEBUG] Transition schema validation successful")
            except Exception as e:
                print(f"[DEBUG] Transition schema validation failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Transition schema validation failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Transition schema validation failed: {str(e)}"
                )

            # Upload converted workflow to GCS
            print("[DEBUG] Uploading converted workflow to GCS")
            try:
                gcs_response = file_upload.upload_json_as_file(converted_workflow, "workflows")
                workflow_url = gcs_response.get("publicUrl")
                print("[DEBUG] Converted workflow upload successful")
                if not workflow_url:
                    print("[DEBUG] Failed to get public URL for converted workflow")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        "Failed to obtain public URL from GCS for converted workflow"
                    )
                    return workflow_pb2.CreateWorkflowResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for converted workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] Converted workflow GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for converted workflow: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"GCS upload failed for converted workflow: {str(e)}"
                )

            # Create new workflow
            print("[DEBUG] Creating new workflow object")
            new_workflow = Workflow(
                # Primary Fields
                name=request.name,
                workflow_url=workflow_url,
                builder_url=builder_url,
                start_nodes=[json.loads(node_json) for node_json in request.start_nodes],
                # Access Control
                owner_id=request.owner.id,
                user_ids=[request.owner.id],
                owner_type=workflow_pb2.WorkflowOwnerType.Name(request.owner_type).lower(),
                # Template Reference
                workflow_template_id=None,  # Set to None for new workflows
                template_owner_id=None,  # Set to None for new workflows
                is_imported=False,  # Default to False for new workflows
                # Additional Fields - Remove version field as it's now handled by WorkflowVersion
                # Task 1: is_changes_marketplace - False for new workflows (in sync)
                is_changes_marketplace=False,
                # Task 2: is_customizable - True for new workflows created from scratch
                is_customizable=True,
                # Task 3: auto_version_on_update - False for new workflows (no auto-versioning)
                auto_version_on_update=False,
                # Additional Fields
                tags=list(request.tags) if request.tags else [],
            )

            print("[DEBUG] Adding workflow to database")
            db.add(new_workflow)
            db.flush()  # Flush to get the workflow ID

            # Create v1 version automatically
            print("[DEBUG] Creating v1 version for the workflow")
            v1_version = WorkflowVersion(
                workflow_id=new_workflow.id,
                version_number="1.0.0",
                name=request.name,
                description=request.description if hasattr(request, 'description') and request.description else None,
                workflow_url=workflow_url,
                builder_url=builder_url,
                start_nodes=[json.loads(node_json) for node_json in request.start_nodes],
                category=workflow_pb2.WorkflowCategory.Name(request.category).lower() if hasattr(request, 'category') and request.category else None,
                tags=list(request.tags) if request.tags else [],
                changelog="Initial version",
            )

            db.add(v1_version)
            db.flush()  # Flush to get the version ID

            # Set the current_version_id in the workflow
            new_workflow.current_version_id = v1_version.id

            db.commit()
            db.refresh(new_workflow)
            db.refresh(v1_version)
            print(f"[DEBUG] Workflow and v1 version saved to database successfully. Version ID: {v1_version.id}")

            # Send Kafka notification
            print("[DEBUG] Sending Kafka notification")
            print(f"[DEBUG] WORKFLOW CREATED {new_workflow.id}")
            # try:
            #     self.kafka_producer.send_email_event_unified(
            #         email_type=SendEmailTypeEnum.WORKFLOW_CREATED.value,
            #         data={
            #             "emailId": request.owner.email,
            #             "userName": request.owner.full_name,
            #             "userId": request.owner.id,
            #             "fcmToken": request.owner.fcm_token,
            #             "workflowId": new_workflow.id,
            #             "workflowName": new_workflow.name,
            #             "title": "New Workflow Created",
            #             "body": f"Your workflow '{new_workflow.name}' has been created successfully.",
            #             "link": f"{settings.FRONTEND_URL}/workflows/{new_workflow.id}",
            #             "logo": f"{settings.FRONTEND_URL}/assets/logo.png",
            #         },
            #         action=["sendNotification", "sendWorkflowEmail"],
            #     )
            #     print("[DEBUG] Kafka notification sent successfully")
            # except Exception as e:
            #     print(f"[DEBUG] Failed to send Kafka notification: {str(e)}")
            #     # Continue even if notification fails

            print("[DEBUG] Workflow creation completed successfully")
            return workflow_pb2.CreateWorkflowResponse(
                success=True,
                message=f"Workflow '{request.name}' created successfully with v1.0.0 version",
                workflow=self._workflow_to_protobuf(new_workflow, db),
            )

        except Exception as e:
            print(f"[DEBUG] Unexpected error in createWorkflow: {str(e)}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("workflow_creation_failed", error=str(e))
            return workflow_pb2.CreateWorkflowResponse(
                success=False, message=f"Workflow creation failed: {str(e)}"
            )
        finally:
            print("[DEBUG] Closing database connection")
            db.close()

    def getWorkflow(
        self, request: workflow_pb2.GetWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.WorkflowResponse:
        """
        Retrieve a workflow by its ID.

        Args:
            request (workflow_pb2.GetWorkflowRequest): The request containing the workflow ID and optional user_id.
            context (grpc.ServicerContext): The gRPC context for handling the request.

        Returns:
            workflow_pb2.WorkflowResponse: The response containing the workflow details if found.

        Raises:
            grpc.RpcError: If the workflow is not found or an unexpected error occurs.
        """
        db = self.get_db()
        logger.info(
            "get_workflow_request",
            workflow_id=request.id,
            user_id=request.user_id if request.HasField("user_id") else None,
        )
        try:
            # Start with base query
            query = db.query(Workflow).filter(Workflow.id == request.id)

            # If user_id is provided, add filter to match owner_id
            if request.HasField("user_id") and request.user_id:
                logger.info("filtering_by_user_id", user_id=request.user_id)
                query = query.filter(Workflow.owner_id == request.user_id)

            # Execute query
            workflow = query.first()

            if workflow is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.WorkflowResponse(success=False, message="Workflow not found")

            logger.info("workflow_retrieved", workflow_id=workflow.id)
            workflow11 = self._workflow_to_protobuf(workflow, db)
            print(f"[WORKFLOW TO PROTOBUF] {workflow11}")
            return workflow_pb2.WorkflowResponse(
                success=True,
                message=f"Workflow {workflow.name} retrieved successfully",
                workflow=self._workflow_to_protobuf(workflow, db),
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.WorkflowResponse(success=False, message="Internal Server Error")
        finally:
            db.close()

    def deleteWorkflow(
        self, request: workflow_pb2.DeleteWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.DeleteWorkflowResponse:
        """
        Deletes a workflow from the database.

        Args:
            request (workflow_pb2.DeleteWorkflowRequest): The request object containing the workflow ID to be deleted.
            context (grpc.ServicerContext): The gRPC context for handling request status and errors.

        Returns:
            workflow_pb2.DeleteWorkflowResponse: A response object indicating the success or failure of the deletion.

        Raises:
            grpc.StatusCode.NOT_FOUND: If the specified workflow does not exist.
            grpc.StatusCode.INTERNAL: If an internal error occurs during the deletion process.
        """
        db = self.get_db()
        logger.info("delete_workflow_request", workflow_id=request.id)
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.id).first()
            if workflow is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.DeleteWorkflowResponse(
                    success=False, message=f"Workflow not found"
                )

            db.delete(workflow)
            db.commit()
            logger.info("workflow_deleted", workflow_id=workflow.id)

            # # Send Kafka event for notifications
            # self.kafka_producer.send_email_event_unified(
            #     email_type=SendEmailTypeEnum.WORKFLOW_DELETED.value,
            #     data={
            #         "emailId": request.owner.email,
            #         "userName": request.owner.full_name,
            #         "userId": request.owner.id,
            #         "fcmToken": request.owner.fcm_token,
            #         "workflowId": request.id,
            #         "workflowName": workflow.name,
            #         "title": "Workflow Deleted",
            #         "body": f"Your workflow '{workflow.name}' has been deleted.",
            #         "link": f"{settings.FRONTEND_URL}/workflows",
            #         "logo": f"{settings.FRONTEND_URL}/assets/logo.png",
            #     },
            #     action=["sendNotification", "sendWorkflowEmail"],
            # )

            logger.info("kafka_event_sent", event_type="WORKFLOW_DELETED")

            return workflow_pb2.DeleteWorkflowResponse(
                success=True, message=f"Workflow {workflow.name} deleted successfully"
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.DeleteWorkflowResponse(
                success=False, message="Internal server error"
            )
        finally:
            db.close()

    def listWorkflows(
        self, request: workflow_pb2.ListWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowsResponse:
        """
        Retrieve a paginated list of workflows with optional filtering by user_id.

        Args:
            request (workflow_pb2.ListWorkflowsRequest): The request object containing pagination details and filters.
            context (grpc.ServicerContext): The gRPC context for handling errors and metadata.

        Returns:
            workflow_pb2.ListWorkflowsResponse: A response containing the list of workflows, total count, current page, and total pages.

        Raises:
            grpc.StatusCode.INTERNAL: If any error occurs during the database query.
        """
        db = self.get_db()
        page = request.page
        page_size = request.page_size
        logger.info(
            "list_workflows_request",
            page=page,
            page_size=page_size,
            user_id=request.user_id if request.HasField("user_id") else None,
        )
        try:
            # Start with a base query
            query = db.query(Workflow)

            # Apply user_id filter if provided
            if request.HasField("user_id") and request.user_id:
                logger.info("filtering_by_user_id", user_id=request.user_id)
                query = query.filter(Workflow.owner_id == request.user_id)

            # Apply filters if provided
            if request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                logger.info("filtering_by_category", category=category_value)
                query = query.filter(Workflow.category == category_value)

            if request.status:
                status_value = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                logger.info("filtering_by_status", status=status_value)
                query = query.filter(Workflow.status == status_value)

            if request.visibility:
                visibility_value = workflow_pb2.WorkflowVisibility.Name(request.visibility).lower()
                logger.info("filtering_by_visibility", visibility=visibility_value)
                query = query.filter(Workflow.visibility == visibility_value)

            # Add search filter for name, description, and category
            if request.search:
                search_term = f"%{request.search}%"
                logger.info("filtering_by_search", search=request.search)
                query = query.filter(
                    db.or_(
                        Workflow.name.ilike(search_term),
                        Workflow.description.ilike(search_term),
                        Workflow.category.ilike(search_term),
                    )
                )

            # Add tags filter
            if request.tags:
                logger.info("filtering_by_tags", tags=request.tags)
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(Workflow.tags.contains(request.tags))

            # Get total count with filters applied
            total = query.count()

            # Apply pagination
            workflows = query.offset((page - 1) * page_size).limit(page_size).all()

            total_pages = (total + page_size - 1) // page_size  # Calculate total pages

            workflow_list = [self._workflow_to_protobuf(workflow, db) for workflow in workflows]

            print(f"[DEBUG] Workflow list: {workflow_list}")

            logger.info("workflows_retrieved", total=total, page=page, total_pages=total_pages)

            return workflow_pb2.ListWorkflowsResponse(
                workflows=workflow_list,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            print(f"[DEBUG] Unexpected error in listWorkflows: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("Internal_server_error", error=e)
            return workflow_pb2.ListWorkflowsResponse()  # Return empty response on error
        finally:
            db.close()

    def listWorkflowsByUserId(
        self, request: workflow_pb2.ListWorkflowsByUserIdRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowsResponse:
        """
        Retrieve a paginated list of workflows for user only.

        Args:
            request (workflow_pb2.ListWorkflowsByUserIdRequest): The request object containing pagination details.
            context (grpc.ServicerContext): The gRPC context for handling errors and metadata.

        Returns:
            workflow_pb2.ListWorkflowsResponse: A response containing the list of workflows, total count, current page, and total pages.

        Raises:
            grpc.StatusCode.INTERNAL: If any error occurs during the database query.
        """
        db = self.get_db()
        logger.info("list_workflows_by_user_request", owner_id=request.owner_id)
        try:
            # Pagination setup
            page = request.page
            page_size = request.page_size
            offset = (page - 1) * page_size

            # Start with a base query
            query = db.query(Workflow)
            print(f"[DEBUG] List Workflows by User ID Request: {request.category}")
            print(
                f"[DEBUG] List Workflows by User ID Request: {workflow_pb2.WorkflowCategory.Name(request.category).lower()}"
            )
            # Apply filters if provided
            if request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                logger.info("filtering_by_category", category=category_value)
                query = query.filter(Workflow.category == category_value)

            if request.status:
                status_value = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                logger.info("filtering_by_status", status=status_value)
                query = query.filter(Workflow.status == status_value)

            if request.visibility:
                visibility_value = workflow_pb2.WorkflowVisibility.Name(request.visibility).lower()
                logger.info("filtering_by_visibility", visibility=visibility_value)
                query = query.filter(Workflow.visibility == visibility_value)
            # Get total count before pagination
            total = query.count()

            logger.info("total workflows count for user_id", total=total)

            # Apply pagination
            workflows = query.offset(offset).limit(page_size).all()

            # Calculate total pages
            total_pages = (total + page_size - 1) // page_size

            # Convert workflows to protobuf objects
            protobuf_workflows = [self._workflow_to_protobuf(workflow, db) for workflow in workflows]

            logger.info("list_workflows_by_user_response", workflows=protobuf_workflows)

            return workflow_pb2.ListWorkflowsResponse(
                success=True,
                workflows=protobuf_workflows,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            print(f"[DEBUG] Unexpected error in listWorkflowsByUserId: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return workflow_pb2.ListWorkflowsResponse()
        finally:
            db.close()

    def getWorkflowsByIds(
        self, request: workflow_pb2.GetWorkflowsByIdsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetWorkflowsByIdsResponse:
        logger.info("get_workflows_by_ids_request", ids_count=len(request.ids))
        print(f"[DEBUG] gRPC Servicer: getWorkflowsByIds called with IDs: {request.ids}")

        if not request.ids:
            logger.warn("get_workflows_by_ids_request_empty_ids")
            return workflow_pb2.GetWorkflowsByIdsResponse(
                workflows=[], total=0, success=True, message="No IDs provided to fetch."
            )

        try:
            db = self.get_db()
            # Query Workflows by the provided IDs
            workflow_models = db.query(Workflow).filter(Workflow.id.in_(request.ids)).all()

            total = len(workflow_models)

            # Convert to protobuf format
            # You'll need a helper similar to _mcp_to_protobuf for workflows
            workflow_list_proto = [self._workflow_to_protobuf(wf, db) for wf in workflow_models]

            print(f"[DEBUG] Workflow list: {workflow_list_proto}")
            logger.info(
                "workflows_retrieved_by_ids",
                retrieved_count=len(workflow_list_proto),
                requested_count=len(request.ids),
            )

            return workflow_pb2.GetWorkflowsByIdsResponse(
                success=True,
                message=f"Successfully retrieved workflows.",
                workflows=workflow_list_proto,
                total=total,
            )
        except Exception as e:
            print(f"[DEBUG] Error fetching workflows by IDs: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error while fetching workflows by IDs: {str(e)}")
            return workflow_pb2.GetWorkflowsByIdsResponse(
                success=False, message="Internal server error."
            )
        # No finally block needed for db.close() due to context manager

    def _workflow_to_protobuf(self, workflow: Workflow, db=None) -> workflow_pb2.Workflow:
        """Convert a Workflow model instance to a protobuf Workflow message."""
        # Get current version information if available
        current_version_number = None
        if workflow.current_version_id:
            try:
                # Use provided db session or create a new one
                db_session = db if db else self.get_db()
                current_version = (
                    db_session.query(WorkflowVersion)
                    .filter(WorkflowVersion.id == workflow.current_version_id)
                    .first()
                )
                if current_version:
                    current_version_number = current_version.version_number
                # Only close if we created the session
                if not db:
                    db_session.close()
            except Exception as e:
                logger.warning(f"Failed to get current version for workflow {workflow.id}: {str(e)}")

        return workflow_pb2.Workflow(
            id=str(workflow.id),  # Convert UUID to string
            name=workflow.name,
            description=workflow.description,
            workflow_url=workflow.workflow_url,
            builder_url=workflow.builder_url,
            owner_id=workflow.owner_id,
            user_ids=workflow.user_ids if workflow.user_ids else [],
            owner_type=workflow.owner_type,
            start_nodes=(
                [json.dumps(node) for node in workflow.start_nodes] if workflow.start_nodes else []
            ),
            # Template Reference fields - Always include these fields
            workflow_template_id=(
                str(workflow.workflow_template_id) if workflow.workflow_template_id else None
            ),
            template_owner_id=(
                str(workflow.template_owner_id) if workflow.template_owner_id else None
            ),
            is_imported=workflow.is_imported if workflow.is_imported else False,
            # Metadata
            is_changes_marketplace=(
                workflow.is_changes_marketplace if workflow.is_changes_marketplace else False
            ),
            is_customizable=workflow.is_customizable if workflow.is_customizable else False,
            version=current_version_number if current_version_number else (workflow.version if workflow.version else "1.0.0"),
            visibility=workflow.visibility,
            category=workflow.category,
            tags=workflow.tags if workflow.tags else [],
            status=workflow.status,
            # Timestamps
            created_at=workflow.created_at.isoformat() if workflow.created_at else "",
            updated_at=workflow.updated_at.isoformat() if workflow.updated_at else "",
        )



    def _create_marketplace_listing_from_workflow(
        self,
        db,
        workflow: Workflow,
    ) -> WorkflowMarketplaceListing | None:
        """
        Creates a marketplace listing for a workflow using its current version.
        """
        if not workflow.owner_id:
            logger.error(f"Workflow {workflow.id} has no owner_id, cannot create marketplace listing.")
            return None

        if not workflow.current_version_id:
            logger.error(f"Workflow {workflow.id} has no current version, cannot create marketplace listing.")
            return None

        # Get the current version
        current_version = (
            db.query(WorkflowVersion)
            .filter(WorkflowVersion.id == workflow.current_version_id)
            .first()
        )

        if not current_version:
            logger.error(f"Current version {workflow.current_version_id} not found for workflow {workflow.id}")
            return None

        # Check if a marketplace listing already exists for this workflow and version
        existing_listing = (
            db.query(WorkflowMarketplaceListing)
            .filter(
                WorkflowMarketplaceListing.workflow_id == workflow.id,
                WorkflowMarketplaceListing.workflow_version_id == current_version.id,
                WorkflowMarketplaceListing.listed_by_user_id == workflow.owner_id,
            )
            .first()
        )

        if existing_listing:
            # Reactivate existing listing if it was deactivated
            existing_listing.status = WorkflowStatusEnum.ACTIVE
            existing_listing.visibility = WorkflowVisibilityEnum.PUBLIC
            db.add(existing_listing)
            logger.info(f"Reactivated existing marketplace listing {existing_listing.id}")
            return existing_listing

        # Create new marketplace listing
        logger.info(f"Creating new marketplace listing for workflow {workflow.id} version {current_version.id}")
        new_listing = WorkflowMarketplaceListing(
            workflow_id=workflow.id,
            workflow_version_id=current_version.id,
            listed_by_user_id=workflow.owner_id,
            title=current_version.name,
            description=current_version.description or "No description provided.",
            image_url=current_version.image_url,
            category=current_version.category,
            tags=current_version.tags if current_version.tags else [],
            use_count=0,
            execution_count=0,
            average_rating=0.0,
            visibility=WorkflowVisibilityEnum.PUBLIC,
            status=WorkflowStatusEnum.ACTIVE,
        )

        db.add(new_listing)
        db.flush()  # Ensure ID is available

        logger.info(f"Created marketplace listing {new_listing.id} for workflow {workflow.id}")
        return new_listing

    def _create_new_version_from_workflow(
        self,
        db,
        workflow: Workflow,
    ) -> WorkflowVersion | None:
        """
        Creates a new version from the current workflow state.
        Automatically increments the version number.
        """
        try:
            # Get the current version to determine the next version number
            current_version = None
            if workflow.current_version_id:
                current_version = (
                    db.query(WorkflowVersion)
                    .filter(WorkflowVersion.id == workflow.current_version_id)
                    .first()
                )

            # Determine the next version number
            if current_version:
                # Parse current version and increment
                current_version_parts = current_version.version_number.split('.')
                if len(current_version_parts) == 3:
                    major, minor, patch = map(int, current_version_parts)
                    # For now, increment minor version for updates
                    new_version_number = f"{major}.{minor + 1}.0"
                else:
                    # Fallback if version format is unexpected
                    new_version_number = "1.1.0"
            else:
                # No current version found, start with 1.1.0 (since 1.0.0 should have been created)
                new_version_number = "1.1.0"

            # Create new version
            new_version = WorkflowVersion(
                workflow_id=workflow.id,
                version_number=new_version_number,
                name=workflow.name,
                description=workflow.description,
                workflow_url=workflow.workflow_url,
                builder_url=workflow.builder_url,
                start_nodes=workflow.start_nodes if workflow.start_nodes else [],
                category=workflow.category,
                tags=workflow.tags if workflow.tags else [],
                changelog=f"Updated workflow - version {new_version_number}",
            )

            db.add(new_version)
            db.flush()  # Get the ID

            logger.info(f"Created new version {new_version_number} for workflow {workflow.id}")
            return new_version

        except Exception as e:
            logger.error(f"Failed to create new version for workflow {workflow.id}: {str(e)}")
            return None

    def toggleWorkflowVisibility(
        self, request: workflow_pb2.ToggleWorkflowVisibilityRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ToggleWorkflowVisibilityResponse:
        db = self.get_db()
        logger.info(
            "toggle_workflow_visibility_request",
            workflow_id=request.workflow_id,
            owner_id=request.owner.id,
        )
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()

            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found.")
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Workflow not found."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Permission denied. You are not the owner."
                )

            message = ""
            if workflow.visibility == WorkflowVisibilityEnum.PRIVATE:
                # --- Going from PRIVATE to PUBLIC ---

                # Constraint: Cannot publish if cloned from another's template and minimally altered
                if (
                    workflow.is_imported
                    and workflow.template_owner_id
                    and workflow.template_owner_id != workflow.owner_id
                ):
                    err_msg = (
                        "This workflow was created from a template owned by another party. "
                        "To share your version publicly, please ensure it is significantly customized. "
                        "Direct republishing of minimally-altered cloned templates is restricted."
                    )
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(err_msg)
                    return workflow_pb2.ToggleWorkflowVisibilityResponse(
                        success=False, message=err_msg
                    )

                workflow.visibility = WorkflowVisibilityEnum.PUBLIC
                workflow.is_changes_marketplace = True  # Default to True when making public

                # Create marketplace listing for the current version
                marketplace_listing = self._create_marketplace_listing_from_workflow(
                    db, workflow
                )
                if not marketplace_listing:
                    db.rollback()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to create marketplace listing.")
                    return workflow_pb2.ToggleWorkflowVisibilityResponse(
                        success=False, message="Failed to prepare workflow for public visibility."
                    )

                message = f"Workflow '{workflow.name}' is now PUBLIC and listed in the marketplace."

            elif workflow.visibility == WorkflowVisibilityEnum.PUBLIC:
                # --- Going from PUBLIC to PRIVATE ---
                workflow.visibility = WorkflowVisibilityEnum.PRIVATE
                # workflow.is_changes_marketplace = False # Optional: Reset this flag

                # Deactivate marketplace listings for this workflow
                marketplace_listings = (
                    db.query(WorkflowMarketplaceListing)
                    .filter(
                        WorkflowMarketplaceListing.workflow_id == workflow.id,
                        WorkflowMarketplaceListing.listed_by_user_id == workflow.owner_id,
                    )
                    .all()
                )

                for listing in marketplace_listings:
                    listing.status = WorkflowStatusEnum.INACTIVE
                    db.add(listing)
                    logger.info(
                        f"Deactivated marketplace listing {listing.id} as workflow {workflow.id} made private."
                    )

                message = f"Workflow '{workflow.name}' is now PRIVATE and removed from marketplace."
            else:
                # Should not happen if visibility is always one of the defined enums
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(
                    f"Workflow {workflow.id} has an unknown visibility state: {workflow.visibility}"
                )
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Workflow has an unknown visibility state."
                )

            db.commit()
            db.refresh(workflow)
            # if 'template' in locals() and template: db.refresh(template)
            # if 'linked_template' in locals() and linked_template: db.refresh(linked_template)

            return workflow_pb2.ToggleWorkflowVisibilityResponse(
                success=True,
                message=message,
                workflow=self._workflow_to_protobuf(workflow),  # Ensure this reflects new state
            )

        except Exception as e:
            db.rollback()
            logger.error("toggle_workflow_visibility_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to toggle workflow visibility: {str(e)}")
            return workflow_pb2.ToggleWorkflowVisibilityResponse(
                success=False, message=f"Failed to toggle workflow visibility: {str(e)}"
            )
        finally:
            db.close()

    def updateWorkflow(
        self, request: workflow_pb2.UpdateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowResponse:
        db = self.get_db()
        logger.info(
            "update_workflow_request", workflow_id=request.id, update_mask=request.update_mask.paths
        )

        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.id} not found")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message=f"Workflow with ID {request.id} not found"
                )

            # Ownership check (assuming request.owner is present and has an id)
            if not hasattr(request, "owner") or not request.owner.id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Owner information missing in request.")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message="Owner information missing."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message="Permission denied."
                )

            file_upload_service = GCSUploadService()  # Initialize GCS service
            template_relevant_fields_changed = False
            version_relevant_fields_changed = False

            # Process workflow_data only if it's in the update_mask
            if "workflow_data" in request.update_mask.paths:
                print("[DEBUG] 'workflow_data' is in update_mask. Processing...")
                if not request.workflow_data:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "workflow_data field is in update_mask but no data provided."
                    )
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message="workflow_data provided in mask but is empty."
                    )
                try:
                    parsed_workflow_data = json.loads(request.workflow_data)
                    print("[DEBUG] Successfully parsed workflow_data JSON for PATCH")
                except json.JSONDecodeError as e:
                    print(f"[DEBUG] JSON parsing failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid JSON format in workflow_data")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message="Invalid JSON format in workflow_data"
                    )

                # Upload original workflow to GCS
                try:
                    gcs_response_builder = file_upload_service.upload_json_as_file(
                        parsed_workflow_data, "workflow_builders"
                    )
                    builder_url = gcs_response_builder.get("publicUrl")
                    if not builder_url:
                        raise Exception("Failed to get public URL from GCS for builder workflow")
                    workflow.builder_url = builder_url  # Update the model field
                    print(f"[DEBUG] GCS builder upload successful for PATCH: {builder_url}")
                except Exception as e:
                    # (Error handling as before, but specific to this block)
                    print(f"[DEBUG] GCS builder upload failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"GCS upload failed for builder workflow: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"GCS upload failed for builder workflow: {str(e)}"
                    )

                # Convert workflow to transition schema
                try:
                    converted_workflow = convert_workflow_to_transition_schema(parsed_workflow_data)
                    print("[DEBUG] Workflow conversion successful for PATCH")
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Workflow conversion failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Workflow schema conversion failed: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"Workflow schema conversion failed: {str(e)}"
                    )

                # Validate converted workflow
                try:
                    validate_transition_schema(
                        data_input=converted_workflow,
                        schema_path="app/utils/shared/json_schemas/transition_schema.json",
                    )
                    print("[DEBUG] Transition schema validation successful for PATCH")
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Transition schema validation failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Transition schema validation failed: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"Transition schema validation failed: {str(e)}"
                    )

                # Upload converted workflow to GCS
                try:
                    gcs_response_workflow = file_upload_service.upload_json_as_file(
                        converted_workflow, "workflows"
                    )
                    workflow_url = gcs_response_workflow.get("publicUrl")
                    if not workflow_url:
                        raise Exception("Failed to get public URL from GCS for converted workflow")
                    workflow.workflow_url = workflow_url  # Update the model field
                    print(
                        f"[DEBUG] Converted workflow GCS upload successful for PATCH: {workflow_url}"
                    )
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Converted GCS workflow upload failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"GCS upload failed for converted workflow: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"GCS upload failed for converted workflow: {str(e)}"
                    )

                template_relevant_fields_changed = True
                version_relevant_fields_changed = True

            # Update other fields based on FieldMask
            for field_path in request.update_mask.paths:
                if field_path == "name":
                    workflow.name = request.name
                    version_relevant_fields_changed = True
                elif field_path == "description":
                    workflow.description = request.description
                    version_relevant_fields_changed = True
                elif field_path == "start_nodes":
                    workflow.start_nodes = [
                        json.loads(node_json) for node_json in request.start_nodes
                    ]
                    version_relevant_fields_changed = True
                elif field_path == "user_ids":
                    workflow.user_ids = list(request.user_ids) if request.user_ids else []
                elif field_path == "visibility":
                    workflow.visibility = workflow_pb2.WorkflowVisibility.Name(
                        request.visibility
                    ).lower()
                elif field_path == "category":
                    workflow.category = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                elif field_path == "tags":  # tags is repeated string in proto, array in DB
                    workflow.tags = list(request.tags) if request.tags else []
                elif field_path == "status":
                    workflow.status = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                elif field_path == "version":
                    workflow.version = request.version
                elif field_path == "is_changes_marketplace" and request.HasField(
                    "is_changes_marketplace"
                ):
                    # This allows explicitly setting this flag via update.
                    # Ensure it only applies if workflow is public.
                    if workflow.visibility == WorkflowVisibilityEnum.PUBLIC:
                        workflow.is_changes_marketplace = request.is_changes_marketplace
                    else:
                        logger.info(
                            f"is_changes_marketplace update ignored for workflow {workflow.id} "
                            "as it's not a public workflow."
                        )
                elif field_path == "auto_version_on_update":
                    workflow.auto_version_on_update = request.auto_version_on_update
                elif field_path == "is_customizable" and request.HasField("is_customizable"):
                    # Allow updating is_customizable field
                    workflow.is_customizable = request.is_customizable

            # Update marketplace listing if this workflow is public and has marketplace changes enabled
            if (
                template_relevant_fields_changed
                and workflow.visibility == WorkflowVisibilityEnum.PUBLIC
                and workflow.is_changes_marketplace
            ):
                logger.info(f"Updating marketplace listing for public workflow {workflow.id}.")
                # Update the marketplace listing with the new workflow version
                marketplace_listing = self._create_marketplace_listing_from_workflow(db, workflow)
                if marketplace_listing:
                    logger.info(f"Updated marketplace listing {marketplace_listing.id} for workflow {workflow.id}")
                else:
                    logger.warning(f"Failed to update marketplace listing for workflow {workflow.id}")

            # Create new version if version-relevant fields were changed AND auto_version_on_update is True
            if version_relevant_fields_changed and workflow.auto_version_on_update:
                print("[DEBUG] Creating new version due to significant changes and auto_version_on_update=True")
                new_version = self._create_new_version_from_workflow(db, workflow)
                if new_version:
                    # Update workflow's current_version_id to point to the new version
                    workflow.current_version_id = new_version.id
                    print(f"[DEBUG] Created new version {new_version.version_number} with ID: {new_version.id}")
            elif version_relevant_fields_changed and not workflow.auto_version_on_update:
                print("[DEBUG] Version-relevant fields changed but auto_version_on_update=False, updating in place")

            db.add(workflow)  # Add workflow to session
            db.commit()

            # Task 1: Update derived workflows if this workflow has marketplace changes enabled
            if (
                template_relevant_fields_changed
                and workflow.visibility == WorkflowVisibilityEnum.PUBLIC
                and workflow.is_changes_marketplace
            ):
                self._update_derived_workflows_change_status(db, workflow)

            db.refresh(workflow)
            # if 'template' in locals() and template: db.refresh(template)

            version_message = ""
            if version_relevant_fields_changed and workflow.auto_version_on_update:
                version_message = " and created new version"
            elif version_relevant_fields_changed and not workflow.auto_version_on_update:
                version_message = " (updated in place - auto versioning disabled)"

            return workflow_pb2.UpdateWorkflowResponse(
                success=True, message=f"Workflow {workflow.name} updated successfully{version_message}"
            )

        except Exception as e:
            if db.is_active:
                db.rollback()
            logger.error("workflow_update_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error during workflow update: {str(e)}")
            return workflow_pb2.UpdateWorkflowResponse(
                success=False, message=f"Workflow update failed: {str(e)}"
            )
        finally:
            db.close()

    def _update_derived_workflows_change_status(self, db: Session, source_workflow: Workflow):
        """
        Update is_changes_marketplace flag for all workflows derived from the source workflow.
        This is called when the source workflow is updated with template-relevant changes.
        
        Uses multiple comparison methods to detect meaningful changes:
        1. Timestamp comparison (primary)
        2. Content hash comparison (future enhancement)
        3. Version comparison (if available)
        """
        try:
            # Get all workflows that were cloned from this source workflow
            derived_workflows = (
                db.query(Workflow)
                .filter(
                    Workflow.workflow_template_id == source_workflow.id,
                    Workflow.is_imported == True
                )
                .all()
            )

            logger.info(
                f"Found {len(derived_workflows)} derived workflows for source {source_workflow.id}"
            )

            # For each derived workflow, compare with source and set is_changes_marketplace
            for derived_workflow in derived_workflows:
                has_changes = self._detect_workflow_changes(derived_workflow, source_workflow)
                
                if has_changes:
                    derived_workflow.is_changes_marketplace = True
                    logger.info(
                        f"Marked derived workflow {derived_workflow.id} as having marketplace changes"
                    )
                else:
                    logger.info(
                        f"No significant changes detected for derived workflow {derived_workflow.id}"
                    )

            db.commit()

        except Exception as e:
            logger.error(f"Failed to update derived workflows change status: {str(e)}")
            db.rollback()
    def getMarketplaceWorkflows(
        self, request: workflow_pb2.GetMarketplaceWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetMarketplaceWorkflowsResponse:
        """
        Retrieves a paginated list of public workflow templates for the marketplace.

        Args:
            request: The request containing pagination, search, and filter parameters
            context: The gRPC context for handling errors

        Returns:
            Response containing the list of marketplace workflows and pagination metadata
        """
        db = self.get_db()
        logger.info(
            "get_marketplace_workflows_request",
            request=request,
        )

        try:
            # Start with a base query for public marketplace listings
            query = db.query(WorkflowMarketplaceListing).filter(
                WorkflowMarketplaceListing.visibility == WorkflowVisibilityEnum.PUBLIC,
                WorkflowMarketplaceListing.status == WorkflowStatusEnum.ACTIVE,
            )

            # Apply search filter if provided
            if request.HasField("search") and request.search:
                search_term = f"%{request.search}%"
                query = query.filter(
                    (WorkflowMarketplaceListing.title.ilike(search_term))
                    | (WorkflowMarketplaceListing.description.ilike(search_term))
                )

            # Apply category filter if provided
            if request.HasField("category") and request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                query = query.filter(WorkflowMarketplaceListing.category == category_value)

            # Apply tags filter if provided
            if request.tags:
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(WorkflowMarketplaceListing.tags.contains(request.tags))

            # Apply sorting
            if request.HasField("sort_by") and request.sort_by:
                sort_by = request.sort_by
                if sort_by == "NEWEST":
                    query = query.order_by(WorkflowMarketplaceListing.created_at.desc())
                elif sort_by == "OLDEST":
                    query = query.order_by(WorkflowMarketplaceListing.created_at.asc())
                elif sort_by == "MOST_POPULAR":
                    query = query.order_by(WorkflowMarketplaceListing.use_count.desc())
                elif sort_by == "HIGHEST_RATED":
                    query = query.order_by(WorkflowMarketplaceListing.average_rating.desc())
                else:
                    # Default to newest
                    query = query.order_by(WorkflowMarketplaceListing.created_at.desc())
            else:
                # Default sorting by newest
                query = query.order_by(WorkflowMarketplaceListing.created_at.desc())

            # Get total count with filters applied
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = min(request.page_size, 100) if request.page_size > 0 else 10

            listings = query.offset((page - 1) * page_size).limit(page_size).all()

            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size if total > 0 else 1
            has_next = page < total_pages
            has_prev = page > 1

            # Convert marketplace listings to protobuf format
            listing_list = [
                self._listing_to_marketplace_workflow(listing) for listing in listings
            ]

            logger.info(
                "marketplace_workflows_retrieved",
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
            )

            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=True,
                message="Marketplace workflows retrieved successfully",
                workflows=listing_list,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_prev=has_prev,
                next_page=page + 1 if has_next else 0,
                prev_page=page - 1 if has_prev else 0,
            )

        except Exception as e:
            logger.error("get_marketplace_workflows_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve marketplace workflows: {str(e)}")
            return workflow_pb2.GetMarketplaceWorkflowsResponse(
                success=False,
                message=f"Failed to retrieve marketplace workflows: {str(e)}",
                workflows=[],
                total=0,
                page=request.page,
                page_size=request.page_size,
                total_pages=0,
                has_next=False,
                has_prev=False,
            )
        finally:
            db.close()

    def _listing_to_marketplace_workflow(
        self, listing: WorkflowMarketplaceListing
    ) -> workflow_pb2.MarketplaceWorkflow:
        """Convert a WorkflowMarketplaceListing model instance to a protobuf MarketplaceWorkflow message."""
        # Get the workflow version details
        workflow_version = None
        if listing.workflow_version_id:
            try:
                db = self.get_db()
                workflow_version = (
                    db.query(WorkflowVersion)
                    .filter(WorkflowVersion.id == listing.workflow_version_id)
                    .first()
                )
                db.close()
            except Exception as e:
                logger.warning(f"Failed to get workflow version for listing {listing.id}: {str(e)}")

        return workflow_pb2.MarketplaceWorkflow(
            id=str(listing.id),
            name=listing.title,
            description=listing.description if listing.description else "",
            image_url=listing.image_url if listing.image_url else "",
            workflow_url=workflow_version.workflow_url if workflow_version else "",
            builder_url=workflow_version.builder_url if workflow_version else "",
            start_nodes=(
                [json.dumps(node) for node in workflow_version.start_nodes] if workflow_version and workflow_version.start_nodes else []
            ),
            category=listing.category if listing.category else "",
            tags=listing.tags if listing.tags else [],
            created_at=listing.created_at.isoformat() if listing.created_at else "",
            updated_at=listing.updated_at.isoformat() if listing.updated_at else "",
            owner_id=listing.listed_by_user_id,
            average_rating=listing.average_rating if listing.average_rating else 0,
            use_count=listing.use_count if listing.use_count else 0,
            execution_count=listing.execution_count if listing.execution_count else 0,
            visibility="PUBLIC",
            version=workflow_version.version_number if workflow_version else "1.0.0",
            status=listing.status if listing.status else "active",
        )

    def updateWorkflowSettings(
        self, request: workflow_pb2.UpdateWorkflowSettingsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowSettingsResponse:
        db = self.get_db()
        logger.info(
            "update_workflow_settings_request",
            workflow_id=request.workflow_id,
            owner_id=request.owner.id,
        )
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()

            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found.")
                return workflow_pb2.UpdateWorkflowSettingsResponse(
                    success=False, message="Workflow not found."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.UpdateWorkflowSettingsResponse(
                    success=False, message="Permission denied."
                )

            updated_fields_count = 0
            if request.HasField("is_changes_marketplace"):  # Check if the optional field is set
                # Check if workflow is public and can have marketplace changes
                if workflow.visibility != WorkflowVisibilityEnum.PUBLIC:
                    msg = (
                        "Cannot set 'is_changes_marketplace' as this workflow is not public."
                    )
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(msg)
                    return workflow_pb2.UpdateWorkflowSettingsResponse(success=False, message=msg)

                if workflow.is_changes_marketplace != request.is_changes_marketplace:
                    workflow.is_changes_marketplace = request.is_changes_marketplace
                    logger.info(
                        f"Workflow {workflow.id} is_changes_marketplace set to {request.is_changes_marketplace}"
                    )
                    updated_fields_count += 1

            if request.HasField("status"):
                if workflow.status != workflow_pb2.WorkflowStatus.Name(request.status).lower():
                    workflow.status = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                    logger.info(f"Workflow {workflow.id} status set to {request.status}")
                    updated_fields_count += 1

            if updated_fields_count > 0:
                db.add(workflow)
                db.commit()
                db.refresh(workflow)
                message = "Workflow settings updated successfully."
            else:
                message = "No settings were changed."

            return workflow_pb2.UpdateWorkflowSettingsResponse(success=True, message=message)
        except Exception as e:
            if "db" in locals():
                db.rollback()
            logger.error("update_workflow_settings_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to update workflow settings: {str(e)}")
            return workflow_pb2.UpdateWorkflowSettingsResponse(
                success=False, message=f"Failed to update workflow settings: {str(e)}"
            )
        finally:
            db.close()

    def rateWorkflow(
        self, request: workflow_pb2.RateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.RateWorkflowResponse:
        """
        Rate a workflow and update its average rating.

        Args:
            request: Contains the workflow ID, user ID, and rating value
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated average rating
        """
        db = self.get_db()
        try:
            logger.info(
                "rate_workflow_request",
                workflow_id=request.workflow_id,
                user_id=request.user_id,
                rating=request.rating,
            )

            # Validate rating value (between 1.0 and 5.0)
            if request.rating < 1.0 or request.rating > 5.0:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Rating must be between 1.0 and 5.0")
                return workflow_pb2.RateWorkflowResponse(
                    success=False, message="Rating must be between 1.0 and 5.0", average_rating=0.0
                )

            # Check if workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.RateWorkflowResponse(
                    success=False,
                    message=f"Workflow with ID {request.workflow_id} not found",
                    average_rating=0.0,
                )

            # Check if user has already rated this workflow
            existing_rating = (
                db.query(WorkflowRating)
                .filter(
                    WorkflowRating.workflow_id == request.workflow_id,
                    WorkflowRating.user_id == request.user_id,
                )
                .first()
            )

            if existing_rating:
                # Update existing rating
                old_rating = existing_rating.rating
                existing_rating.rating = request.rating
                existing_rating.updated_at = datetime.now(timezone.utc)
                db.commit()
                logger.info(
                    "updated_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    old_rating=old_rating,
                    new_rating=request.rating,
                )
            else:
                # Create new rating
                new_rating = WorkflowRating(
                    workflow_id=request.workflow_id, user_id=request.user_id, rating=request.rating
                )
                db.add(new_rating)
                db.commit()
                logger.info(
                    "created_workflow_rating",
                    workflow_id=request.workflow_id,
                    user_id=request.user_id,
                    rating=request.rating,
                )

            # Calculate new average rating
            ratings = (
                db.query(WorkflowRating)
                .filter(WorkflowRating.workflow_id == request.workflow_id)
                .all()
            )
            total_rating = sum(r.rating for r in ratings)
            average_rating = total_rating / len(ratings) if ratings else 0.0

            # Update workflow with new average rating
            workflow.average_rating = average_rating
            workflow.updated_at = datetime.now(timezone.utc)
            db.commit()

            # If this workflow was created from a source workflow, update marketplace listings for that source
            if workflow.workflow_template_id:
                # Find marketplace listings for the source workflow
                marketplace_listings = (
                    db.query(WorkflowMarketplaceListing)
                    .filter(WorkflowMarketplaceListing.workflow_id == workflow.workflow_template_id)
                    .all()
                )

                for marketplace_listing in marketplace_listings:
                    # Get all ratings for all workflows created from this source workflow
                    listing_workflows = (
                        db.query(Workflow)
                        .filter(Workflow.workflow_template_id == workflow.workflow_template_id)
                        .all()
                    )

                    listing_workflow_ids = [w.id for w in listing_workflows]

                    if listing_workflow_ids:
                        listing_ratings = (
                            db.query(WorkflowRating)
                            .filter(WorkflowRating.workflow_id.in_(listing_workflow_ids))
                            .all()
                        )

                        if listing_ratings:
                            listing_total_rating = sum(r.rating for r in listing_ratings)
                            listing_average_rating = listing_total_rating / len(listing_ratings)

                            marketplace_listing.average_rating = listing_average_rating
                            marketplace_listing.updated_at = datetime.now(timezone.utc)
                            db.commit()

                            logger.info(
                                "updated_marketplace_listing_rating",
                                listing_id=marketplace_listing.id,
                                average_rating=listing_average_rating,
                            )

            return workflow_pb2.RateWorkflowResponse(
                success=True,
                message=f"Rating for workflow {workflow.name} updated successfully",
                average_rating=average_rating,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error rating workflow: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.RateWorkflowResponse(
                success=False, message="Failed to update workflow rating", average_rating=0.0
            )
        finally:
            db.close()

    def useWorkflow(
        self, request: workflow_pb2.UseWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UseWorkflowResponse:
        """
        Create a copy of a marketplace workflow for a user and increment the listing's use count.

        Args:
            request: Contains the marketplace listing ID and user ID
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated use count
        """
        db = self.get_db()
        try:
            logger.info(
                "use_workflow_request", listing_id=request.workflow_id, user_id=request.user_id
            )

            # Check if marketplace listing exists and is public/active
            marketplace_listing = (
                db.query(WorkflowMarketplaceListing)
                .filter(
                    WorkflowMarketplaceListing.id == request.workflow_id,
                    WorkflowMarketplaceListing.visibility == WorkflowVisibilityEnum.PUBLIC,
                    WorkflowMarketplaceListing.status == WorkflowStatusEnum.ACTIVE,
                )
                .first()
            )

            if not marketplace_listing:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Public marketplace listing with ID {request.workflow_id} not found"
                )
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message=f"Public marketplace listing with ID {request.workflow_id} not found",
                    use_count=0,
                )

            # Get the workflow version associated with this listing
            workflow_version = (
                db.query(WorkflowVersion)
                .filter(WorkflowVersion.id == marketplace_listing.workflow_version_id)
                .first()
            )

            if not workflow_version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Workflow version for marketplace listing {request.workflow_id} not found"
                )
                return workflow_pb2.UseWorkflowResponse(
                    success=False,
                    message=f"Workflow version for marketplace listing not found",
                    use_count=0,
                )

            # Get the source workflow to check its customizability
            source_workflow = (
                db.query(Workflow)
                .filter(Workflow.id == marketplace_listing.workflow_id)
                .first()
            )

            # Determine if the cloned workflow should be customizable
            # Only customizable if source workflow allows it
            is_customizable_for_clone = (
                source_workflow.is_customizable if source_workflow else False
            )

            # Create a new workflow based on the marketplace listing
            # Reference the source workflow instead of the marketplace listing
            new_workflow = Workflow(
                name=f"{marketplace_listing.title} - Copy",
                description=marketplace_listing.description,
                workflow_url=workflow_version.workflow_url,
                builder_url=workflow_version.builder_url,
                start_nodes=workflow_version.start_nodes if workflow_version.start_nodes else [],
                owner_id=request.user_id,
                user_ids=[request.user_id],
                owner_type="user",  # Assuming the user is of type "user"
                workflow_template_id=marketplace_listing.workflow_id,  # Reference to source workflow
                template_owner_id=marketplace_listing.listed_by_user_id,
                is_imported=True,  # Mark as imported from marketplace
                visibility=WorkflowVisibilityEnum.PRIVATE,  # Default to private for the copy
                category=marketplace_listing.category,
                tags=marketplace_listing.tags if marketplace_listing.tags else [],
                # Task 1: is_changes_marketplace - False for cloned workflows (initially in sync)
                is_changes_marketplace=False,
                # Task 2: is_customizable - Check source workflow customizability
                is_customizable=is_customizable_for_clone,  # Based on source workflow
                # Task 3: auto_version_on_update - False for cloned workflows
                auto_version_on_update=False,
                status=WorkflowStatusEnum.ACTIVE,
            )

            db.add(new_workflow)
            db.flush()  # Get the workflow ID

            # Create v1 version for the new workflow
            v1_version = WorkflowVersion(
                workflow_id=new_workflow.id,
                version_number="1.0.0",
                name=new_workflow.name,
                description=new_workflow.description,
                workflow_url=workflow_version.workflow_url,
                builder_url=workflow_version.builder_url,
                start_nodes=workflow_version.start_nodes if workflow_version.start_nodes else [],
                category=marketplace_listing.category,
                tags=marketplace_listing.tags if marketplace_listing.tags else [],
                changelog="Initial version created from marketplace listing",
                is_customizable=is_customizable_for_clone,
            )

            db.add(v1_version)
            db.flush()  # Get the version ID

            # Set the current_version_id in the workflow
            new_workflow.current_version_id = v1_version.id

            # Increment the marketplace listing's use count
            marketplace_listing.use_count += 1
            marketplace_listing.updated_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(new_workflow)
            db.refresh(marketplace_listing)

            logger.info(
                "workflow_created_from_marketplace",
                new_workflow_id=new_workflow.id,
                marketplace_listing_id=marketplace_listing.id,
                user_id=request.user_id,
                new_use_count=marketplace_listing.use_count,
            )

            return workflow_pb2.UseWorkflowResponse(
                success=True,
                message=f"New workflow created from marketplace listing '{marketplace_listing.title}'",
                use_count=marketplace_listing.use_count,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error using marketplace workflow: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.UseWorkflowResponse(
                success=False, message="Failed to create workflow from marketplace listing", use_count=0
            )
        finally:
            db.close()

    # Version Management Methods

    def listWorkflowVersions(
        self, request: workflow_pb2.ListWorkflowVersionsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowVersionsResponse:
        """
        List all versions of a workflow with permission checking.
        Supports both workflow owners and marketplace users.
        """
        db = self.get_db()
        logger.info(
            "list_workflow_versions_request",
            workflow_id=request.workflow_id,
            user_id=request.user_id,
            page=request.page,
            page_size=request.page_size,
        )

        try:
            # First, check if the workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.ListWorkflowVersionsResponse(
                    success=False,
                    message="Workflow not found",
                    versions=[],
                    total=0,
                    page=request.page,
                    total_pages=0,
                )

            # Permission check: Owner can see all versions, marketplace users can only see public workflow versions
            is_owner = workflow.owner_id == request.user_id
            is_public_workflow = workflow.visibility == WorkflowVisibilityEnum.PUBLIC

            if not is_owner and not is_public_workflow:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You don't have permission to view versions of this private workflow")
                return workflow_pb2.ListWorkflowVersionsResponse(
                    success=False,
                    message="Permission denied: Cannot view versions of private workflow",
                    versions=[],
                    total=0,
                    page=request.page,
                    total_pages=0,
                )

            # Query versions for the workflow
            query = db.query(WorkflowVersion).filter(WorkflowVersion.workflow_id == request.workflow_id)

            # Get total count
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            versions = query.order_by(WorkflowVersion.created_at.desc()).offset(offset).limit(page_size).all()

            # Calculate total pages
            total_pages = (total + page_size - 1) // page_size

            # Convert to protobuf
            version_protos = []
            for version in versions:
                is_current = version.id == workflow.current_version_id
                version_proto = self._workflow_version_to_protobuf(version, is_current)
                version_protos.append(version_proto)

            logger.info(
                "workflow_versions_retrieved",
                workflow_id=request.workflow_id,
                total=total,
                page=page,
                total_pages=total_pages,
                is_owner=is_owner,
            )

            return workflow_pb2.ListWorkflowVersionsResponse(
                success=True,
                message=f"Retrieved {len(version_protos)} versions for workflow",
                versions=version_protos,
                total=total,
                page=page,
                total_pages=total_pages,
                current_version_id=workflow.current_version_id or "",
            )

        except Exception as e:
            logger.error(f"Failed to list workflow versions: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list workflow versions: {str(e)}")
            return workflow_pb2.ListWorkflowVersionsResponse(
                success=False,
                message=f"Failed to list workflow versions: {str(e)}",
                versions=[],
                total=0,
                page=request.page,
                total_pages=0,
            )
        finally:
            db.close()

    def getWorkflowVersion(
        self, request: workflow_pb2.GetWorkflowVersionRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetWorkflowVersionResponse:
        """
        Get details of a specific workflow version with permission checking.
        Supports both workflow owners and marketplace users.
        """
        db = self.get_db()
        logger.info(
            "get_workflow_version_request",
            workflow_id=request.workflow_id,
            version_id=request.version_id,
            user_id=request.user_id,
        )

        try:
            # First, check if the workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.GetWorkflowVersionResponse(
                    success=False,
                    message="Workflow not found",
                )

            # Permission check: Owner can see all versions, marketplace users can only see public workflow versions
            is_owner = workflow.owner_id == request.user_id
            is_public_workflow = workflow.visibility == WorkflowVisibilityEnum.PUBLIC

            if not is_owner and not is_public_workflow:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You don't have permission to view versions of this private workflow")
                return workflow_pb2.GetWorkflowVersionResponse(
                    success=False,
                    message="Permission denied: Cannot view versions of private workflow",
                )

            # Get the specific version
            version = (
                db.query(WorkflowVersion)
                .filter(
                    WorkflowVersion.workflow_id == request.workflow_id,
                    WorkflowVersion.id == request.version_id,
                )
                .first()
            )

            if not version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Version with ID {request.version_id} not found for workflow {request.workflow_id}")
                return workflow_pb2.GetWorkflowVersionResponse(
                    success=False,
                    message="Version not found",
                )

            # Convert to protobuf
            is_current = version.id == workflow.current_version_id
            version_proto = self._workflow_version_to_protobuf(version, is_current)

            logger.info(
                "workflow_version_retrieved",
                workflow_id=request.workflow_id,
                version_id=request.version_id,
                is_owner=is_owner,
                is_current=is_current,
            )

            return workflow_pb2.GetWorkflowVersionResponse(
                success=True,
                message=f"Retrieved version {version.version_number}",
                version=version_proto,
            )

        except Exception as e:
            logger.error(f"Failed to get workflow version: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get workflow version: {str(e)}")
            return workflow_pb2.GetWorkflowVersionResponse(
                success=False,
                message=f"Failed to get workflow version: {str(e)}",
            )
        finally:
            db.close()

    def switchWorkflowVersion(
        self, request: workflow_pb2.SwitchWorkflowVersionRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.SwitchWorkflowVersionResponse:
        """
        Switch the current version of a workflow. Only owners can switch versions.
        """
        db = self.get_db()
        logger.info(
            "switch_workflow_version_request",
            workflow_id=request.workflow_id,
            version_id=request.version_id,
            user_id=request.user_id,
        )

        try:
            # First, check if the workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=False,
                    message="Workflow not found",
                )

            # Permission check: Only owners can switch versions
            if workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Only the workflow owner can switch versions")
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=False,
                    message="Permission denied: Only the workflow owner can switch versions",
                )

            # Check if the target version exists
            target_version = (
                db.query(WorkflowVersion)
                .filter(
                    WorkflowVersion.workflow_id == request.workflow_id,
                    WorkflowVersion.id == request.version_id,
                )
                .first()
            )

            if not target_version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Version with ID {request.version_id} not found for workflow {request.workflow_id}")
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=False,
                    message="Target version not found",
                )

            # Check if it's already the current version
            if workflow.current_version_id == request.version_id:
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=True,
                    message=f"Version {target_version.version_number} is already the current version",
                    new_current_version=self._workflow_version_to_protobuf(target_version, True),
                )

            # Update the current version
            old_version_id = workflow.current_version_id
            workflow.current_version_id = request.version_id

            # Update workflow fields from the target version
            workflow.name = target_version.name
            workflow.description = target_version.description
            workflow.workflow_url = target_version.workflow_url
            workflow.builder_url = target_version.builder_url
            workflow.start_nodes = target_version.start_nodes
            workflow.category = target_version.category
            workflow.tags = target_version.tags

            db.commit()
            db.refresh(workflow)
            db.refresh(target_version)

            logger.info(
                "workflow_version_switched",
                workflow_id=request.workflow_id,
                old_version_id=old_version_id,
                new_version_id=request.version_id,
                user_id=request.user_id,
            )

            return workflow_pb2.SwitchWorkflowVersionResponse(
                success=True,
                message=f"Successfully switched to version {target_version.version_number}",
                new_current_version=self._workflow_version_to_protobuf(target_version, True),
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to switch workflow version: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to switch workflow version: {str(e)}")
            return workflow_pb2.SwitchWorkflowVersionResponse(
                success=False,
                message=f"Failed to switch workflow version: {str(e)}",
            )
        finally:
            db.close()

    def createWorkflowVersion(
        self, request: workflow_pb2.CreateWorkflowVersionRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateWorkflowVersionResponse:
        """
        Create a new version for a workflow. Only owners can create versions.
        """
        db = self.get_db()
        logger.info(
            "create_workflow_version_request",
            workflow_id=request.workflow_id,
            user_id=request.user_id,
            auto_increment=request.auto_increment,
            version_number=request.version_number,
        )

        try:
            # First, check if the workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.CreateWorkflowVersionResponse(
                    success=False,
                    message="Workflow not found",
                )

            # Permission check: Only owners can create versions
            if workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Only the workflow owner can create versions")
                return workflow_pb2.CreateWorkflowVersionResponse(
                    success=False,
                    message="Permission denied: Only the workflow owner can create versions",
                )

            # Determine version number
            if request.auto_increment:
                # Get the latest version to auto-increment
                latest_version = (
                    db.query(WorkflowVersion)
                    .filter(WorkflowVersion.workflow_id == request.workflow_id)
                    .order_by(WorkflowVersion.created_at.desc())
                    .first()
                )

                if latest_version:
                    # Parse and increment version
                    version_parts = latest_version.version_number.split('.')
                    if len(version_parts) == 3:
                        major, minor, patch = map(int, version_parts)
                        new_version_number = f"{major}.{minor + 1}.0"
                    else:
                        new_version_number = "1.1.0"
                else:
                    new_version_number = "1.1.0"
            else:
                # Use provided version number
                if not request.version_number:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("version_number is required when auto_increment is false")
                    return workflow_pb2.CreateWorkflowVersionResponse(
                        success=False,
                        message="version_number is required when auto_increment is false",
                    )
                new_version_number = request.version_number

            # Check if version number already exists
            existing_version = (
                db.query(WorkflowVersion)
                .filter(
                    WorkflowVersion.workflow_id == request.workflow_id,
                    WorkflowVersion.version_number == new_version_number,
                )
                .first()
            )

            if existing_version:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details(f"Version {new_version_number} already exists for this workflow")
                return workflow_pb2.CreateWorkflowVersionResponse(
                    success=False,
                    message=f"Version {new_version_number} already exists for this workflow",
                )

            # Create new version from current workflow state
            new_version = WorkflowVersion(
                workflow_id=workflow.id,
                version_number=new_version_number,
                name=request.name if request.name else workflow.name,
                description=request.description if request.description else workflow.description,
                workflow_url=workflow.workflow_url,
                builder_url=workflow.builder_url,
                start_nodes=workflow.start_nodes if workflow.start_nodes else [],
                category=workflow.category,
                tags=workflow.tags if workflow.tags else [],
                changelog=request.changelog if request.changelog else f"Version {new_version_number}",
            )

            db.add(new_version)
            db.commit()
            db.refresh(new_version)

            logger.info(
                "workflow_version_created",
                workflow_id=request.workflow_id,
                version_id=new_version.id,
                version_number=new_version_number,
                user_id=request.user_id,
            )

            return workflow_pb2.CreateWorkflowVersionResponse(
                success=True,
                message=f"Successfully created version {new_version_number}",
                version=self._workflow_version_to_protobuf(new_version, False),
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to create workflow version: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to create workflow version: {str(e)}")
            return workflow_pb2.CreateWorkflowVersionResponse(
                success=False,
                message=f"Failed to create workflow version: {str(e)}",
            )
        finally:
            db.close()

    def _workflow_version_to_protobuf(self, version: WorkflowVersion, is_current: bool) -> workflow_pb2.WorkflowVersion:
        """Convert a WorkflowVersion model instance to a protobuf WorkflowVersion message."""
        return workflow_pb2.WorkflowVersion(
            id=str(version.id),
            workflow_id=str(version.workflow_id),
            version_number=version.version_number,
            name=version.name,
            description=version.description or "",
            workflow_url=version.workflow_url,
            builder_url=version.builder_url,
            start_nodes=[json.dumps(node) for node in version.start_nodes] if version.start_nodes else [],
            category=version.category or "",
            tags=version.tags if version.tags else [],
            changelog=version.changelog or "",
            status=version.status or "active",
            is_customizable=version.is_customizable if hasattr(version, 'is_customizable') else True,
            created_at=version.created_at.isoformat() if version.created_at else "",
            is_current=is_current,
        )
    def pullUpdatesFromSource(
        self, request: workflow_pb2.PullUpdatesFromSourceRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.PullUpdatesFromSourceResponse:
        """
        Pull updates from the source workflow for a cloned workflow.
        
        This method updates a cloned workflow with the latest changes from its source workflow
        and resets the is_changes_marketplace flag to False.
        
        Args:
            request: Contains workflow_id and user_id
            context: gRPC context for error handling
            
        Returns:
            Response with success status and updated workflow
        """
        db = self.get_db()
        logger.info(
            "pull_updates_from_source_request",
            workflow_id=request.workflow_id,
            user_id=request.user_id
        )
        
        try:
            # Get the cloned workflow
            cloned_workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not cloned_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Workflow not found"
                )
            
            # Check ownership
            if cloned_workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this workflow.")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Permission denied"
                )
            
            # Check if this is a cloned workflow
            if not cloned_workflow.is_imported or not cloned_workflow.workflow_template_id:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("This workflow is not cloned from a source workflow")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="This workflow is not cloned from a source workflow"
                )
            
            # Get the source workflow
            source_workflow = db.query(Workflow).filter(
                Workflow.id == cloned_workflow.workflow_template_id
            ).first()
            if not source_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source workflow not found")
                return workflow_pb2.PullUpdatesFromSourceResponse(
                    success=False, message="Source workflow not found"
                )
            
            # Update the cloned workflow with source workflow data
            cloned_workflow.description = source_workflow.description
            cloned_workflow.workflow_url = source_workflow.workflow_url
            cloned_workflow.builder_url = source_workflow.builder_url
            cloned_workflow.start_nodes = source_workflow.start_nodes
            cloned_workflow.category = source_workflow.category
            cloned_workflow.tags = source_workflow.tags
            cloned_workflow.is_changes_marketplace = False  # Reset the flag
            cloned_workflow.updated_at = datetime.now(timezone.utc)
            
            db.add(cloned_workflow)
            db.commit()
            db.refresh(cloned_workflow)
            
            logger.info(
                f"Successfully pulled updates for workflow {cloned_workflow.id} from source {source_workflow.id}"
            )
            
            return workflow_pb2.PullUpdatesFromSourceResponse(
                success=True,
                message="Successfully pulled updates from source workflow. Your workflow is now in sync.",
                updated_workflow=self._workflow_to_proto(cloned_workflow)
            )
            
        except Exception as e:
            if db.is_active:
                db.rollback()
            logger.error("pull_updates_from_source_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.PullUpdatesFromSourceResponse(
                success=False, message=f"Failed to pull updates: {str(e)}"
            )
        finally:
            db.close()

    def checkForUpdates(
        self, request: workflow_pb2.CheckForUpdatesRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CheckForUpdatesResponse:
        """
        Check if a cloned workflow has updates available from its source workflow.
        
        Args:
            request: Contains workflow_id and user_id
            context: gRPC context for error handling
            
        Returns:
            Response with update availability information
        """
        db = self.get_db()
        logger.info(
            "check_for_updates_request",
            workflow_id=request.workflow_id,
            user_id=request.user_id
        )
        
        try:
            # Get the cloned workflow
            cloned_workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not cloned_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Workflow not found"
                )
            
            # Check ownership
            if cloned_workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this workflow.")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Permission denied"
                )
            
            # Check if this is a cloned workflow
            if not cloned_workflow.is_imported or not cloned_workflow.workflow_template_id:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("This workflow is not cloned from a source workflow")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="This workflow is not cloned from a source workflow"
                )
            
            # Get the source workflow
            source_workflow = db.query(Workflow).filter(
                Workflow.id == cloned_workflow.workflow_template_id
            ).first()
            if not source_workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Source workflow not found")
                return workflow_pb2.CheckForUpdatesResponse(
                    success=False, message="Source workflow not found"
                )
            
            has_updates = cloned_workflow.is_changes_marketplace or False
            message = "Updates available from source workflow" if has_updates else "Workflow is up to date"
            
            return workflow_pb2.CheckForUpdatesResponse(
                success=True,
                message=message,
                has_updates=has_updates,
                source_workflow_id=cloned_workflow.workflow_template_id,
                last_updated=cloned_workflow.updated_at.isoformat() if cloned_workflow.updated_at else "",
                source_last_updated=source_workflow.updated_at.isoformat() if source_workflow.updated_at else ""
            )
            
        except Exception as e:
            logger.error("check_for_updates_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.CheckForUpdatesResponse(
                success=False, message=f"Failed to check for updates: {str(e)}"
            )
        finally:
            db.close()

    def _detect_workflow_changes(self, derived_workflow: Workflow, source_workflow: Workflow) -> bool:
        """
        Detect if there are meaningful changes between a derived workflow and its source.
        
        This method uses multiple comparison strategies to determine if the source workflow
        has changes that should trigger an update notification for the derived workflow.
        
        Args:
            derived_workflow: The cloned workflow
            source_workflow: The source workflow
            
        Returns:
            True if changes are detected, False otherwise
        """
        try:
            # Strategy 1: Timestamp comparison (primary method)
            # If source was updated after the derived workflow was last synced
            if source_workflow.updated_at > derived_workflow.updated_at:
                logger.info(
                    f"Timestamp change detected: source updated at {source_workflow.updated_at}, "
                    f"derived last updated at {derived_workflow.updated_at}"
                )
                return True
            
            # Strategy 2: Content comparison for key fields
            # Compare important fields that would affect the workflow functionality
            content_fields_changed = (
                derived_workflow.description != source_workflow.description or
                derived_workflow.workflow_url != source_workflow.workflow_url or
                derived_workflow.builder_url != source_workflow.builder_url or
                derived_workflow.start_nodes != source_workflow.start_nodes or
                derived_workflow.category != source_workflow.category or
                derived_workflow.tags != source_workflow.tags
            )
            
            if content_fields_changed:
                logger.info(f"Content changes detected between workflows")
                return True
            
            # Strategy 3: Version comparison (if versions are tracked)
            # This could be enhanced with semantic versioning in the future
            if hasattr(source_workflow, 'version') and hasattr(derived_workflow, 'version'):
                if source_workflow.version != derived_workflow.version:
                    logger.info(
                        f"Version change detected: source v{source_workflow.version}, "
                        f"derived v{derived_workflow.version}"
                    )
                    return True
            
            # No significant changes detected
            return False
            
        except Exception as e:
            logger.error(f"Error detecting workflow changes: {str(e)}")
            # In case of error, assume changes exist to be safe
            return True

    def _generate_content_hash(self, workflow: Workflow) -> str:
        """
        Generate a hash of the workflow's content for comparison purposes.
        
        This method creates a hash based on the workflow's key content fields
        that would affect functionality when changed.
        
        Args:
            workflow: The workflow to hash
            
        Returns:
            SHA256 hash of the workflow content
        """
        import hashlib
        import json
        
        try:
            # Create a dictionary of the key content fields
            content_data = {
                'description': workflow.description or '',
                'workflow_url': workflow.workflow_url or '',
                'builder_url': workflow.builder_url or '',
                'start_nodes': workflow.start_nodes or [],
                'category': workflow.category.value if workflow.category else '',
                'tags': workflow.tags or []
            }
            
            # Convert to JSON string for consistent hashing
            content_json = json.dumps(content_data, sort_keys=True)
            
            # Generate SHA256 hash
            return hashlib.sha256(content_json.encode('utf-8')).hexdigest()
            
        except Exception as e:
            logger.error(f"Error generating content hash: {str(e)}")
            # Return a timestamp-based hash as fallback
            return hashlib.sha256(str(workflow.updated_at).encode('utf-8')).hexdigest()
