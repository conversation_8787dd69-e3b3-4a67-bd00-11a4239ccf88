#!/usr/bin/env python3
"""
Validation script to verify that our workflow service changes are working correctly.
This script validates the code changes without requiring database migrations.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def validate_model_changes():
    """Validate that the Workflow model has the new field and foreign key"""
    try:
        from app.models.workflow import Workflow
        
        # Check if the model has the new field
        workflow_instance = Workflow()
        
        # Validate auto_version_on_update field exists
        if hasattr(workflow_instance, 'auto_version_on_update'):
            print("✅ auto_version_on_update field exists in Workflow model")
        else:
            print("❌ auto_version_on_update field missing from Workflow model")
            return False
            
        # Check if workflow_template_id field exists
        if hasattr(workflow_instance, 'workflow_template_id'):
            print("✅ workflow_template_id field exists in Workflow model")
        else:
            print("❌ workflow_template_id field missing from Workflow model")
            return False
            
        # Check if derived_workflows relationship exists
        if hasattr(workflow_instance, 'derived_workflows'):
            print("✅ derived_workflows relationship exists in Workflow model")
        else:
            print("❌ derived_workflows relationship missing from Workflow model")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import Workflow model: {e}")
        return False
    except Exception as e:
        print(f"❌ Error validating model changes: {e}")
        return False

def validate_workflow_functions():
    """Validate that workflow functions have been updated"""
    try:
        from app.services.workflow_functions import WorkflowFunctions
        
        # Check if WorkflowFunctions class exists
        workflow_functions = WorkflowFunctions()
        print("✅ WorkflowFunctions class imported successfully")
        
        # Check if the updateWorkflow method exists
        if hasattr(workflow_functions, 'updateWorkflow'):
            print("✅ updateWorkflow method exists")
        else:
            print("❌ updateWorkflow method missing")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import WorkflowFunctions: {e}")
        return False
    except Exception as e:
        print(f"❌ Error validating workflow functions: {e}")
        return False

def validate_protobuf_changes():
    """Validate that protobuf files have been updated"""
    try:
        from app.grpc_ import workflow_pb2
        
        # Check if UpdateWorkflowRequest has auto_version_on_update field
        request = workflow_pb2.UpdateWorkflowRequest()
        
        # Try to access the field (this will work if the field exists)
        try:
            request.auto_version_on_update = True
            print("✅ auto_version_on_update field exists in UpdateWorkflowRequest")
        except AttributeError:
            print("❌ auto_version_on_update field missing from UpdateWorkflowRequest")
            return False
            
        # Check if Workflow message has auto_version_on_update field
        workflow_msg = workflow_pb2.Workflow()
        try:
            workflow_msg.auto_version_on_update = True
            print("✅ auto_version_on_update field exists in Workflow message")
        except AttributeError:
            print("❌ auto_version_on_update field missing from Workflow message")
            return False
            
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import workflow_pb2: {e}")
        return False
    except Exception as e:
        print(f"❌ Error validating protobuf changes: {e}")
        return False

def validate_constants():
    """Validate that constants are accessible"""
    try:
        from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum
        
        print("✅ WorkflowVisibilityEnum imported successfully")
        print("✅ WorkflowStatusEnum imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import constants: {e}")
        return False
    except Exception as e:
        print(f"❌ Error validating constants: {e}")
        return False

def validate_file_changes():
    """Validate that the code changes are present in the files"""
    
    # Check workflow_functions.py for auto_version_on_update handling
    try:
        with open('app/services/workflow_functions.py', 'r') as f:
            content = f.read()
            
        if 'auto_version_on_update' in content:
            print("✅ auto_version_on_update found in workflow_functions.py")
        else:
            print("❌ auto_version_on_update not found in workflow_functions.py")
            return False
            
        if 'version_relevant_fields_changed and workflow.auto_version_on_update' in content:
            print("✅ Conditional versioning logic found in workflow_functions.py")
        else:
            print("❌ Conditional versioning logic not found in workflow_functions.py")
            return False
            
        if 'marketplace_listing.workflow_id' in content:
            print("✅ Updated foreign key reference found in workflow_functions.py")
        else:
            print("❌ Updated foreign key reference not found in workflow_functions.py")
            return False
            
        return True
        
    except FileNotFoundError:
        print("❌ workflow_functions.py file not found")
        return False
    except Exception as e:
        print(f"❌ Error reading workflow_functions.py: {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 Validating Workflow Service Changes...")
    print("=" * 50)
    
    all_valid = True
    
    print("\n📋 Validating Model Changes:")
    if not validate_model_changes():
        all_valid = False
    
    print("\n📋 Validating Workflow Functions:")
    if not validate_workflow_functions():
        all_valid = False
    
    print("\n📋 Validating Protobuf Changes:")
    if not validate_protobuf_changes():
        all_valid = False
    
    print("\n📋 Validating Constants:")
    if not validate_constants():
        all_valid = False
    
    print("\n📋 Validating File Changes:")
    if not validate_file_changes():
        all_valid = False
    
    print("\n" + "=" * 50)
    if all_valid:
        print("🎉 All validations passed! The changes are working correctly.")
        print("\n📝 Next Steps:")
        print("1. Apply the database migration manually (see CHANGES_SUMMARY.md)")
        print("2. Test the API endpoints")
        print("3. Deploy the changes")
    else:
        print("❌ Some validations failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
